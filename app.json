{"expo": {"jsEngine": "hermes", "scheme": "tmsapp", "name": "TmsApp", "slug": "tms_app", "version": "1.0.1", "orientation": "portrait", "icon": "./assets/images/icon.png", "userInterfaceStyle": "light", "description": "TMS of Emerald Coast - Professional mental health services and TMS therapy. Book consultations, complete assessments, and access comprehensive mental health care.", "assetBundlePatterns": ["assets/**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.emrald.tms", "buildNumber": "3", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "UIStatusBarStyle": "UIStatusBarStyleLightContent", "UIViewControllerBasedStatusBarAppearance": false}}, "android": {"jsEngine": "hermes", "versionCode": 2, "ndkVersion": "26.1.10909125", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#f6f9fb"}, "edgeToEdgeEnabled": true, "package": "com.emrald.tms", "permissions": ["android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE"]}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#f6f9fb", "imageWidth": 150, "borderRadius": 20}]], "extra": {"router": {}, "eas": {"projectId": "aec7ddf4-9474-4131-9a9a-4b9309bf4176"}}, "runtimeVersion": "1.0.1"}}