# TMS App - Play Store Deployment Guide

## Prerequisites Completed ✅

1. **App Configuration**: Updated to use environment variables
2. **Version Numbers**: Incremented for new release (v1.0.1, versionCode: 2)
3. **EAS Configuration**: Properly configured with environment support
4. **Signing**: Ready for EAS-managed production signing

## Required Setup Steps

### 1. Set Up Environment Variables on EAS

Run these commands to set up your environment variables securely:

```bash
# Install EAS CLI if not already installed
npm install -g eas-cli

# Login to your Expo account
eas login

# Create environment variables for production
eas secret:create --scope project --name EXPO_PUBLIC_SUPABASE_URL --value "https://abpavtpsxwsgelbpnlvp.supabase.co" --environment production

eas secret:create --scope project --name EXPO_PUBLIC_SUPABASE_ANON_KEY --value "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.-0WNNGViYLye8MCqHNwljn1TqySUM4gJ8viyM4xc4wI" --environment production

eas secret:create --scope project --name RESEND_API_KEY --value "re_MvZGF9wY_9RVDB46EHfNM3cFWMpgar2Uv" --environment production --visibility sensitive

# For development environment (optional)
eas secret:create --scope project --name EXPO_PUBLIC_SUPABASE_URL --value "https://abpavtpsxwsgelbpnlvp.supabase.co" --environment development

eas secret:create --scope project --name EXPO_PUBLIC_SUPABASE_ANON_KEY --value "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.-0WNNGViYLye8MCqHNwljn1TqySUM4gJ8viyM4xc4wI" --environment development

eas secret:create --scope project --name RESEND_API_KEY --value "re_MvZGF9wY_9RVDB46EHfNM3cFWMpgar2Uv" --environment development --visibility sensitive
```

### 2. Set Up Production Signing

```bash
# Configure Android signing credentials
eas credentials

# Select Android platform
# Choose "Generate new keystore" for production signing
# EAS will handle the keystore generation and management
```

### 3. Set Up Google Play Store Submission (Required for automated submission)

1. **Create Google Service Account**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable Google Play Developer API
   - Create a service account with Play Developer API access
   - Download the JSON key file

2. **Configure Play Console**:
   - Go to [Google Play Console](https://play.google.com/console/)
   - Go to Setup > API access
   - Link your Google Cloud project
   - Grant access to the service account

3. **Upload Service Account Key to EAS**:
   ```bash
   # Upload the service account JSON file
   eas secret:create --scope project --name GOOGLE_SERVICE_ACCOUNT_KEY --type file --value ./path-to-your-service-account.json --environment production
   ```

4. **Update eas.json** (if using service account file):
   ```json
   {
     "submit": {
       "production": {
         "android": {
           "serviceAccountKeyPath": "./google-service-account.json",
           "track": "internal"
         }
       }
     }
   }
   ```

## Build and Deploy Commands

### Test Build (Recommended first)
```bash
# Build for preview/testing
npm run build:android:preview
```

### Production Build and Submit
```bash
# Build production AAB
npm run build:android:production

# After build completes, submit to Play Store
npm run submit:android
```

### Automated Build and Submit
```bash
# Build and auto-submit in one command
eas build --platform android --profile production --auto-submit
```

## Important Notes

- **First Upload**: You must upload your app manually to Play Store at least once before using automated submission
- **Version Code**: Always increment `versionCode` in app.config.js for each new release
- **Track**: Currently set to "internal" track - change to "alpha", "beta", or "production" as needed
- **Testing**: Always test with preview builds before production submission

## Troubleshooting

- If build fails, check environment variables are set correctly: `eas env:list`
- For local development, pull environment variables: `eas env:pull --environment development`
- Check build logs on EAS dashboard for detailed error information
