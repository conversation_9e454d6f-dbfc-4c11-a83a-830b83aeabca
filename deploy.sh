#!/bin/bash

# TMS App Deployment Script
# This script helps you deploy your app to the Play Store safely

set -e  # Exit on any error

echo "🚀 TMS App Deployment Script"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if EAS CLI is installed
check_eas_cli() {
    print_status "Checking EAS CLI installation..."
    if ! command -v eas &> /dev/null; then
        print_error "EAS CLI is not installed. Please install it first:"
        echo "npm install -g eas-cli"
        exit 1
    fi
    print_success "EAS CLI is installed"
}

# Check if user is logged in to EAS
check_eas_login() {
    print_status "Checking EAS login status..."
    if ! eas whoami &> /dev/null; then
        print_error "You are not logged in to EAS. Please login first:"
        echo "eas login"
        exit 1
    fi
    print_success "Logged in to EAS as: $(eas whoami)"
}

# Check environment variables
check_env_vars() {
    print_status "Checking environment variables..."
    
    # List environment variables
    echo "Current environment variables:"
    eas env:list || {
        print_warning "Could not list environment variables. Make sure you have set them up."
        echo "Run the commands from DEPLOYMENT_GUIDE.md to set up environment variables."
    }
}

# Validate configuration files
validate_config() {
    print_status "Validating configuration files..."
    
    # Check if app.config.js exists
    if [ ! -f "app.config.js" ]; then
        print_error "app.config.js not found!"
        exit 1
    fi
    
    # Check if eas.json exists
    if [ ! -f "eas.json" ]; then
        print_error "eas.json not found!"
        exit 1
    fi
    
    # Check if required assets exist
    if [ ! -f "assets/images/icon.png" ]; then
        print_error "App icon not found at assets/images/icon.png"
        exit 1
    fi
    
    if [ ! -f "assets/images/adaptive-icon.png" ]; then
        print_error "Adaptive icon not found at assets/images/adaptive-icon.png"
        exit 1
    fi
    
    if [ ! -f "assets/images/splash-icon.png" ]; then
        print_error "Splash icon not found at assets/images/splash-icon.png"
        exit 1
    fi
    
    print_success "All configuration files and assets are present"
}

# Build preview version for testing
build_preview() {
    print_status "Building preview version for testing..."
    print_warning "This will create an APK for testing purposes"
    
    read -p "Do you want to proceed with preview build? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        eas build --platform android --profile preview
        print_success "Preview build completed!"
        print_status "You can download and test the APK from the EAS dashboard"
    else
        print_status "Preview build skipped"
    fi
}

# Build production version
build_production() {
    print_status "Building production version..."
    print_warning "This will create an AAB for Play Store submission"
    
    read -p "Do you want to proceed with production build? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        eas build --platform android --profile production
        print_success "Production build completed!"
        print_status "AAB is ready for Play Store submission"
    else
        print_status "Production build skipped"
    fi
}

# Submit to Play Store
submit_to_store() {
    print_status "Submitting to Play Store..."
    print_warning "Make sure you have:"
    echo "  1. Set up Google Service Account"
    echo "  2. Configured Play Console access"
    echo "  3. Uploaded your app manually at least once"
    
    read -p "Do you want to proceed with Play Store submission? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        eas submit --platform android --profile production
        print_success "Submission completed!"
    else
        print_status "Play Store submission skipped"
    fi
}

# Main menu
show_menu() {
    echo
    echo "What would you like to do?"
    echo "1. Run all checks"
    echo "2. Build preview (APK for testing)"
    echo "3. Build production (AAB for Play Store)"
    echo "4. Submit to Play Store"
    echo "5. Full deployment (build + submit)"
    echo "6. Exit"
    echo
}

# Main execution
main() {
    check_eas_cli
    check_eas_login
    
    while true; do
        show_menu
        read -p "Enter your choice (1-6): " choice
        
        case $choice in
            1)
                validate_config
                check_env_vars
                print_success "All checks passed!"
                ;;
            2)
                validate_config
                build_preview
                ;;
            3)
                validate_config
                build_production
                ;;
            4)
                submit_to_store
                ;;
            5)
                validate_config
                build_production
                submit_to_store
                ;;
            6)
                print_status "Goodbye!"
                exit 0
                ;;
            *)
                print_error "Invalid choice. Please enter 1-6."
                ;;
        esac
    done
}

# Run main function
main
