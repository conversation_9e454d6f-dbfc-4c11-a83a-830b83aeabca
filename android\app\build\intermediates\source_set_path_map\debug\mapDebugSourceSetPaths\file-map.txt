com.emrald.tms.app-recyclerview-1.1.0-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\0378842f2cc21c378cc3148f59f4a126\transformed\recyclerview-1.1.0\res
com.emrald.tms.app-frameanimation-3.0.5-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\0a16690d258db9613a75274a14e309d6\transformed\frameanimation-3.0.5\res
com.emrald.tms.app-room-runtime-2.6.1-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa1b3426e5b28e1b2bd38c87cb12a60\transformed\room-runtime-2.6.1\res
com.emrald.tms.app-savedstate-ktx-1.2.1-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\1288064ff67492b09a3a44b6281a8798\transformed\savedstate-ktx-1.2.1\res
com.emrald.tms.app-fragment-1.6.1-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\1318a9d580631d126e0626b3d9b8a200\transformed\fragment-1.6.1\res
com.emrald.tms.app-activity-ktx-1.8.0-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\14d3e5dad6049251f67b6397f91b60e4\transformed\activity-ktx-1.8.0\res
com.emrald.tms.app-android-maps-utils-3.8.2-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\1a0033a94446b3d18e08ac984d148f43\transformed\android-maps-utils-3.8.2\res
com.emrald.tms.app-drawerlayout-1.1.1-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\1b83aab34ef7522c7c4bf2597db3b88a\transformed\drawerlayout-1.1.1\res
com.emrald.tms.app-room-ktx-2.6.1-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\238cbeb8f15b60bba568629bf4f59bb6\transformed\room-ktx-2.6.1\res
com.emrald.tms.app-lifecycle-livedata-2.6.2-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\27553dd2dfc34d4e207908b83817dd8c\transformed\lifecycle-livedata-2.6.2\res
com.emrald.tms.app-sqlite-framework-2.4.0-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\2a53c1eade0a942b65e686c891be224b\transformed\sqlite-framework-2.4.0\res
com.emrald.tms.app-tracing-ktx-1.2.0-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\2da8e71a560d133edd011b3e35cb1825\transformed\tracing-ktx-1.2.0\res
com.emrald.tms.app-sqlite-2.4.0-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\2fc988b9c65f50aafd6fabcfb02e2a7c\transformed\sqlite-2.4.0\res
com.emrald.tms.app-play-services-maps-18.2.0-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\34ccd47af87cbbd27206aac6ae1b312f\transformed\play-services-maps-18.2.0\res
com.emrald.tms.app-constraintlayout-2.0.1-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\3900519c5e037ec04b5c7d2c94e1d4f9\transformed\constraintlayout-2.0.1\res
com.emrald.tms.app-expo.modules.sharing-13.1.5-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\3cdb651c999aa01202dd3503788bb80f\transformed\expo.modules.sharing-13.1.5\res
com.emrald.tms.app-glide-plugin-3.0.5-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\46b8185c7df56fd647214e167a70cef8\transformed\glide-plugin-3.0.5\res
com.emrald.tms.app-expo.modules.splashscreen-0.30.10-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\47168ecc093fa127b0eafa5026ed488c\transformed\expo.modules.splashscreen-0.30.10\res
com.emrald.tms.app-drawee-3.6.0-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\4a33d95c4a57460b316ff27bd4337ed4\transformed\drawee-3.6.0\res
com.emrald.tms.app-autofill-1.1.0-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\5041bc68a267adf4901d597bb73cf199\transformed\autofill-1.1.0\res
com.emrald.tms.app-savedstate-1.2.1-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\582b2cce3ae563b587e963c742918ec9\transformed\savedstate-1.2.1\res
com.emrald.tms.app-appcompat-resources-1.7.0-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\5aecfb4af2c7dbdbaacb250cb9957531\transformed\appcompat-resources-1.7.0\res
com.emrald.tms.app-work-runtime-2.7.1-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\62ea6626556d2a66b671a209ff7463a2\transformed\work-runtime-2.7.1\res
com.emrald.tms.app-lifecycle-viewmodel-2.6.2-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\63c551d7abe5bae60ab149fc0c31bd43\transformed\lifecycle-viewmodel-2.6.2\res
com.emrald.tms.app-lifecycle-viewmodel-ktx-2.6.2-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\66a1f86e43fc062ec4b2c3ec7ce7414d\transformed\lifecycle-viewmodel-ktx-2.6.2\res
com.emrald.tms.app-cardview-1.0.0-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\678f0c68987edf107f1f7c670b16817c\transformed\cardview-1.0.0\res
com.emrald.tms.app-fragment-ktx-1.6.1-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\6c8bade8e180f11e3607265670ebd386\transformed\fragment-ktx-1.6.1\res
com.emrald.tms.app-core-splashscreen-1.2.0-alpha02-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\734ac0732a5fcb113b8e679b747c6a47\transformed\core-splashscreen-1.2.0-alpha02\res
com.emrald.tms.app-avif-3.0.5-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\7d2933aba01cd38982edd82d38fc5ad2\transformed\avif-3.0.5\res
com.emrald.tms.app-gif-3.0.5-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\87ef981a984a50a27117cce804c064f5\transformed\gif-3.0.5\res
com.emrald.tms.app-appcompat-1.7.0-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\88f83b0c1fdef17ed54d11ca14a04e33\transformed\appcompat-1.7.0\res
com.emrald.tms.app-expo.modules.filesystem-18.1.11-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\89b9f80bd44da0533dee1db0de78e9e4\transformed\expo.modules.filesystem-18.1.11\res
com.emrald.tms.app-core-runtime-2.2.0-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\930c094eafa6caa523b4a3816643bd91\transformed\core-runtime-2.2.0\res
com.emrald.tms.app-coordinatorlayout-1.2.0-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\951beb2fb1fc3766975f398beeb8b1a0\transformed\coordinatorlayout-1.2.0\res
com.emrald.tms.app-profileinstaller-1.3.1-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2adb643c66bdb0817403b3d1e36852\transformed\profileinstaller-1.3.1\res
com.emrald.tms.app-emoji2-views-helper-1.3.0-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\9f7c7c676dfb7e1d2f88fdbc78b74405\transformed\emoji2-views-helper-1.3.0\res
com.emrald.tms.app-lifecycle-livedata-core-ktx-2.6.2-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\a8214bdf237bbbc2f9fe1892eb61e591\transformed\lifecycle-livedata-core-ktx-2.6.2\res
com.emrald.tms.app-lifecycle-runtime-ktx-2.6.2-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\ad3cf956cf2bacf95e334bdcc1d3ea34\transformed\lifecycle-runtime-ktx-2.6.2\res
com.emrald.tms.app-tracing-1.2.0-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\b3b7767c7b8ccf14461c9594404c27d0\transformed\tracing-1.2.0\res
com.emrald.tms.app-lifecycle-viewmodel-savedstate-2.6.2-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\b8399f53a93885e7c29a2c965ce9b9ba\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.emrald.tms.app-core-ktx-1.13.1-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\b91cbafe65ca152608ce75f15f75c052\transformed\core-ktx-1.13.1\res
com.emrald.tms.app-awebp-3.0.5-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\bd6d1215497e0fce21c68de3ece1e03a\transformed\awebp-3.0.5\res
com.emrald.tms.app-transition-1.5.0-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\bd6eeb567cdd3ff5f9a771aa50f549c7\transformed\transition-1.5.0\res
com.emrald.tms.app-swiperefreshlayout-1.1.0-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\c2ade1e22d99b948aab24862c3ea5018\transformed\swiperefreshlayout-1.1.0\res
com.emrald.tms.app-core-1.13.1-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\c6f482c6ce57763f6180873ba8bf558c\transformed\core-1.13.1\res
com.emrald.tms.app-media-1.0.0-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\cd63a34c416a60691d31e9441e672d0f\transformed\media-1.0.0\res
com.emrald.tms.app-play-services-base-18.2.0-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\d01614c8721acfa4a13c278e25c2fb9e\transformed\play-services-base-18.2.0\res
com.emrald.tms.app-androidsvg-aar-1.4-47 C:\Users\<USER>\.gradle\caches\8.13\transforms\d39493a1ad2d3968fe93d0116c080f3f\transformed\androidsvg-aar-1.4\res
com.emrald.tms.app-lifecycle-service-2.6.2-48 C:\Users\<USER>\.gradle\caches\8.13\transforms\d785fa2506a3e25afb3abee9b129361f\transformed\lifecycle-service-2.6.2\res
com.emrald.tms.app-startup-runtime-1.1.1-49 C:\Users\<USER>\.gradle\caches\8.13\transforms\d9ab5f9e0633ac1505106a6b2352cb18\transformed\startup-runtime-1.1.1\res
com.emrald.tms.app-emoji2-1.3.0-50 C:\Users\<USER>\.gradle\caches\8.13\transforms\dc13aa19facc7d4ad4e03f7fba8cdbe0\transformed\emoji2-1.3.0\res
com.emrald.tms.app-viewpager2-1.0.0-51 C:\Users\<USER>\.gradle\caches\8.13\transforms\e0067639026cbf1d7a27ec8ef3edde03\transformed\viewpager2-1.0.0\res
com.emrald.tms.app-lifecycle-livedata-core-2.6.2-52 C:\Users\<USER>\.gradle\caches\8.13\transforms\e54495bb00168a658f9cd37c124debf4\transformed\lifecycle-livedata-core-2.6.2\res
com.emrald.tms.app-react-android-0.79.5-debug-53 C:\Users\<USER>\.gradle\caches\8.13\transforms\e5e613c135358825ca859c6b38975850\transformed\react-android-0.79.5-debug\res
com.emrald.tms.app-annotation-experimental-1.4.0-54 C:\Users\<USER>\.gradle\caches\8.13\transforms\e6ada23c91f8682b1d465858580c6db5\transformed\annotation-experimental-1.4.0\res
com.emrald.tms.app-lifecycle-runtime-2.6.2-55 C:\Users\<USER>\.gradle\caches\8.13\transforms\e7b795847c502ea5365ab4a751b303e2\transformed\lifecycle-runtime-2.6.2\res
com.emrald.tms.app-material-1.12.0-56 C:\Users\<USER>\.gradle\caches\8.13\transforms\f56d39baaf826f86df4a54181264ad3d\transformed\material-1.12.0\res
com.emrald.tms.app-lifecycle-process-2.6.2-57 C:\Users\<USER>\.gradle\caches\8.13\transforms\f59bc4cfaf0d62ef7ff8e8f4ac580a33\transformed\lifecycle-process-2.6.2\res
com.emrald.tms.app-activity-1.8.0-58 C:\Users\<USER>\.gradle\caches\8.13\transforms\f852260ec1bc9023cbf6116ede851a3b\transformed\activity-1.8.0\res
com.emrald.tms.app-play-services-basement-18.1.0-59 C:\Users\<USER>\.gradle\caches\8.13\transforms\fbbb5461b1e0ea39a1f39d3dcc9e0b0a\transformed\play-services-basement-18.1.0\res
com.emrald.tms.app-apng-3.0.5-60 C:\Users\<USER>\.gradle\caches\8.13\transforms\fcdce23a0f5d0e0bb0542e7bf8509ae5\transformed\apng-3.0.5\res
com.emrald.tms.app-glide-4.16.0-61 C:\Users\<USER>\.gradle\caches\8.13\transforms\feda5adf821f53719270e4a7f960d091\transformed\glide-4.16.0\res
com.emrald.tms.app-pngs-62 C:\Users\<USER>\Downloads\TMS-App-main\android\app\build\generated\res\pngs\debug
com.emrald.tms.app-resValues-63 C:\Users\<USER>\Downloads\TMS-App-main\android\app\build\generated\res\resValues\debug
com.emrald.tms.app-packageDebugResources-64 C:\Users\<USER>\Downloads\TMS-App-main\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.emrald.tms.app-packageDebugResources-65 C:\Users\<USER>\Downloads\TMS-App-main\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.emrald.tms.app-debug-66 C:\Users\<USER>\Downloads\TMS-App-main\android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.emrald.tms.app-debug-67 C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\debug\res
com.emrald.tms.app-main-68 C:\Users\<USER>\Downloads\TMS-App-main\android\app\src\main\res
com.emrald.tms.app-debug-69 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-70 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\@react-native-community\datetimepicker\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-71 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\@react-native-community\netinfo\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-72 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\expo-constants\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-73 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\expo-eas-client\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-74 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\expo-json-utils\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-75 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\expo-linking\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-76 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\expo-manifests\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-77 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\expo-modules-core\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-78 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\expo-structured-headers\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-79 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\expo-updates-interface\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-80 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\expo-updates\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-81 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\expo\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-82 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-edge-to-edge\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-83 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-84 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-maps\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-85 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-quick-crypto\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-86 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-reanimated\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-87 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-88 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-89 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-svg\android\build\intermediates\packaged_res\debug\packageDebugResources
com.emrald.tms.app-debug-90 C:\Users\<USER>\Downloads\TMS-App-main\node_modules\react-native-webview\android\build\intermediates\packaged_res\debug\packageDebugResources
